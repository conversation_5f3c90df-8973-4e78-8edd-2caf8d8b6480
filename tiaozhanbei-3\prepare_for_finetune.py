import json
import random
from pathlib import Path

def load_dataset(file_path):
    """加载数据集"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            item = json.loads(line.strip())
            data.append(item)
    return data

def split_dataset(data, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1):
    """划分数据集"""
    assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, "比例之和必须为1"
    
    # 随机打乱数据
    random.shuffle(data)
    
    total = len(data)
    train_size = int(total * train_ratio)
    val_size = int(total * val_ratio)
    
    train_data = data[:train_size]
    val_data = data[train_size:train_size + val_size]
    test_data = data[train_size + val_size:]
    
    return train_data, val_data, test_data

def convert_to_finetune_format(data, format_type="alpaca"):
    """转换为微调格式"""
    converted_data = []
    
    for item in data:
        if format_type == "alpaca":
            # Alpaca格式
            converted_item = {
                "instruction": "你是一个水利工程专家，请根据提供的上下文回答问题。",
                "input": f"上下文：{item['context']}\n\n问题：{item['query']}",
                "output": item['response']
            }
        elif format_type == "chatglm":
            # ChatGLM格式
            converted_item = {
                "prompt": f"上下文：{item['context']}\n\n问题：{item['query']}\n\n请作为水利工程专家回答：",
                "response": item['response']
            }
        elif format_type == "qwen":
            # Qwen格式
            converted_item = {
                "system": "你是一个专业的水利工程专家，擅长分析闸门运行数据和水利工程管理问题。",
                "user": f"根据以下上下文回答问题：\n\n{item['context']}\n\n问题：{item['query']}",
                "assistant": item['response']
            }
        else:
            # 保持原格式
            converted_item = item
            
        converted_data.append(converted_item)
    
    return converted_data

def save_dataset(data, file_path):
    """保存数据集"""
    with open(file_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def generate_statistics(train_data, val_data, test_data):
    """生成统计信息"""
    print("📊 数据集划分统计:")
    print(f"   训练集: {len(train_data)} 条 ({len(train_data)/(len(train_data)+len(val_data)+len(test_data))*100:.1f}%)")
    print(f"   验证集: {len(val_data)} 条 ({len(val_data)/(len(train_data)+len(val_data)+len(test_data))*100:.1f}%)")
    print(f"   测试集: {len(test_data)} 条 ({len(test_data)/(len(train_data)+len(val_data)+len(test_data))*100:.1f}%)")
    print(f"   总计: {len(train_data)+len(val_data)+len(test_data)} 条")
    
    # 分析问题类型分布
    def analyze_question_types(data, dataset_name):
        question_patterns = {
            "数据分析类": 0, "时间查询类": 0, "判断类": 0, 
            "流程询问类": 0, "概念解释类": 0, "其他类型": 0
        }
        
        for item in data:
            query = item.get('query', '') if 'query' in item else item.get('user', '')
            
            if any(word in query for word in ["最大", "最小", "平均", "趋势", "变化"]):
                question_patterns["数据分析类"] += 1
            elif any(word in query for word in ["时间", "期间", "什么时候", "何时"]):
                question_patterns["时间查询类"] += 1
            elif any(word in query for word in ["是否", "有没有", "能否", "可以"]):
                question_patterns["判断类"] += 1
            elif any(word in query for word in ["如何", "怎样", "流程", "步骤", "方法"]):
                question_patterns["流程询问类"] += 1
            elif any(word in query for word in ["什么", "哪些", "包括", "含义"]):
                question_patterns["概念解释类"] += 1
            else:
                question_patterns["其他类型"] += 1
        
        print(f"\n📈 {dataset_name}问题类型分布:")
        for qtype, count in question_patterns.items():
            if count > 0:
                print(f"   {qtype}: {count} 条 ({count/len(data)*100:.1f}%)")

    analyze_question_types(train_data, "训练集")
    analyze_question_types(val_data, "验证集")
    analyze_question_types(test_data, "测试集")

def main():
    """主函数"""
    print("🚀 准备微调数据集...")
    
    # 设置随机种子确保可重现
    random.seed(42)
    
    # 加载数据
    input_file = "enhanced_rag_dataset.jsonl"
    data = load_dataset(input_file)
    print(f"✅ 加载了 {len(data)} 条数据")
    
    # 划分数据集
    train_data, val_data, test_data = split_dataset(data)
    
    # 生成统计信息
    generate_statistics(train_data, val_data, test_data)
    
    # 创建输出目录
    output_dir = Path("finetune_data")
    output_dir.mkdir(exist_ok=True)
    
    # 保存原格式数据
    print(f"\n💾 保存原格式数据...")
    save_dataset(train_data, output_dir / "train_original.jsonl")
    save_dataset(val_data, output_dir / "val_original.jsonl")
    save_dataset(test_data, output_dir / "test_original.jsonl")
    
    # 转换并保存不同格式
    formats = ["alpaca", "chatglm", "qwen"]
    
    for fmt in formats:
        print(f"💾 保存 {fmt.upper()} 格式数据...")
        
        train_converted = convert_to_finetune_format(train_data, fmt)
        val_converted = convert_to_finetune_format(val_data, fmt)
        test_converted = convert_to_finetune_format(test_data, fmt)
        
        save_dataset(train_converted, output_dir / f"train_{fmt}.jsonl")
        save_dataset(val_converted, output_dir / f"val_{fmt}.jsonl")
        save_dataset(test_converted, output_dir / f"test_{fmt}.jsonl")
    
    print(f"\n🎉 数据准备完成！")
    print(f"📁 输出目录: {output_dir}")
    print(f"📋 文件列表:")
    for file in sorted(output_dir.glob("*.jsonl")):
        print(f"   {file.name}")
    
    # 生成微调配置建议
    print(f"\n⚙️ 微调参数建议:")
    print(f"   学习率: 1e-5 到 5e-5")
    print(f"   批次大小: 4-8")
    print(f"   训练轮数: 3-5 epochs")
    print(f"   最大长度: 1024 tokens")
    print(f"   梯度累积: 2-4 steps")
    
    print(f"\n🎯 推荐微调框架:")
    print(f"   1. LLaMA-Factory (支持多种模型)")
    print(f"   2. ChatGLM-Tuning (专门针对ChatGLM)")
    print(f"   3. Qwen-Tuning (专门针对Qwen)")
    print(f"   4. Transformers + PEFT (灵活性最高)")
    
    # 显示数据样本
    print(f"\n📝 数据样本 (Alpaca格式):")
    sample = convert_to_finetune_format([train_data[0]], "alpaca")[0]
    print(f"Instruction: {sample['instruction']}")
    print(f"Input: {sample['input'][:100]}...")
    print(f"Output: {sample['output'][:100]}...")

if __name__ == "__main__":
    main()
