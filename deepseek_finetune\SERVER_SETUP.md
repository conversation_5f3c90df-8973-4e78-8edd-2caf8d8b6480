# 服务器部署指南

## 1. 上传文件到服务器

将整个 `deepseek_finetune` 文件夹上传到服务器：

```bash
# 使用scp上传（在本地执行）
scp -r deepseek_finetune/ username@server_ip:/path/to/your/directory/

# 或使用rsync
rsync -avz deepseek_finetune/ username@server_ip:/path/to/your/directory/deepseek_finetune/
```

## 2. 服务器环境准备

### 检查CUDA环境
```bash
nvidia-smi
nvcc --version
```

### 创建Python虚拟环境（推荐）
```bash
# 使用conda
conda create -n deepseek python=3.9
conda activate deepseek

# 或使用venv
python -m venv deepseek_env
source deepseek_env/bin/activate  # Linux
# deepseek_env\Scripts\activate  # Windows
```

## 3. 快速启动

进入项目目录并运行快速启动脚本：

```bash
cd deepseek_finetune
python quick_start.py
```

这个脚本会自动：
- 检查环境要求
- 安装依赖包
- 查找训练数据
- 开始训练

## 4. 手动启动（如果快速启动失败）

### 安装依赖
```bash
pip install -r requirements.txt
```

### 检查数据路径
确保训练数据文件路径正确，编辑 `config.py`：
```python
DATA_PATH = "你的数据文件路径/train_alpaca.jsonl"
```

### 开始训练
```bash
python main.py
```

## 5. 后台运行（推荐）

使用screen或nohup在后台运行：

### 使用screen
```bash
screen -S deepseek_training
python main.py
# 按 Ctrl+A 然后按 D 退出screen
# 重新连接: screen -r deepseek_training
```

### 使用nohup
```bash
nohup python main.py > training.log 2>&1 &
# 查看日志: tail -f training.log
```

## 6. 监控训练进度

### 查看GPU使用情况
```bash
watch -n 1 nvidia-smi
```

### 查看训练日志
```bash
tail -f output/runs/*/events.out.tfevents.*  # tensorboard日志
# 或
tail -f training.log  # 如果使用nohup
```

### 使用tensorboard（可选）
```bash
pip install tensorboard
tensorboard --logdir=output/runs --port=6006
# 然后在浏览器访问: http://server_ip:6006
```

## 7. 配置调优

根据您的服务器配置调整 `config.py`：

### 显存24GB（RTX 4090/A100）
```python
BATCH_SIZE = 4
GRADIENT_ACCUMULATION_STEPS = 4
MAX_LENGTH = 2048
```

### 显存16GB（RTX 4080）
```python
BATCH_SIZE = 2
GRADIENT_ACCUMULATION_STEPS = 8
MAX_LENGTH = 1536
```

### 显存12GB（RTX 4070Ti）
```python
BATCH_SIZE = 1
GRADIENT_ACCUMULATION_STEPS = 16
MAX_LENGTH = 1024
# 在main.py中设置 use_quantization = True
```

## 8. 常见问题解决

### CUDA内存不足
1. 减小 `BATCH_SIZE`
2. 启用量化：在 `main.py` 中设置 `use_quantization=True`
3. 减小 `MAX_LENGTH`

### 网络连接问题（下载模型失败）
1. 设置代理：
```bash
export HF_ENDPOINT=https://hf-mirror.com
```

2. 或手动下载模型到本地，然后修改 `config.py` 中的 `MODEL_NAME`

### 权限问题
```bash
chmod +x run.sh
chmod +x quick_start.py
```

## 9. 训练完成后

### 测试模型
```bash
python inference.py test  # 运行预设测试
python inference.py       # 交互式测试
```

### 保存模型
训练完成的模型会保存在 `./output/` 目录下，包含：
- LoRA权重文件
- 配置文件
- 训练检查点

## 10. 预估训练时间

- RTX 4090: 约1-2小时（167条数据，3轮）
- A100: 约30-60分钟
- RTX 3090: 约2-3小时

实际时间取决于数据量和配置参数。
