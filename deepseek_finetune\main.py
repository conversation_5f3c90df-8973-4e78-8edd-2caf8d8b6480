"""
主训练脚本
"""

import os
import sys
import torch
import random
import numpy as np
from config import Config
from model_setup import ModelSetup
from data_processor import DataProcessor
from trainer import CustomTrainer

def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

def check_gpu():
    """检查GPU状态"""
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"GPU可用: {gpu_count} 块")
        for i in range(gpu_count):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            total_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i} 内存: {total_memory:.1f} GB")

        # 检查显存是否足够
        if total_memory < 20:
            print("警告: GPU显存可能不足，建议启用量化训练")
            return False
        return True
    else:
        print("警告: 未检测到GPU，将使用CPU训练（非常慢）")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("DeepSeek 水利工程模型微调")
    print("=" * 50)
    
    # 设置随机种子
    config = Config()
    set_seed(config.SEED)
    
    # 检查GPU
    gpu_sufficient = check_gpu()
    
    # 创建必要目录
    config.create_dirs()
    
    # 初始化wandb（如果启用）
    if config.USE_WANDB:
        import wandb
        wandb.init(
            project=config.WANDB_PROJECT,
            name=config.WANDB_RUN_NAME,
            config=vars(config)
        )
    
    try:
        # 1. 设置模型和tokenizer
        print("\n1. 加载模型和tokenizer...")
        model_setup = ModelSetup()
        
        # 强制启用量化以节省显存
        use_quantization = True
        print("🔧 启用4bit量化训练以节省显存")
        model, tokenizer = model_setup.setup_model_and_tokenizer(use_quantization)
        
        # 2. 处理数据
        print("\n2. 处理训练数据...")
        data_processor = DataProcessor(tokenizer)
        train_dataset, val_dataset = data_processor.prepare_datasets(config.DATA_PATH)
        
        # 打印数据样本
        data_processor.print_sample(train_dataset)
        
        # 3. 开始训练
        print("\n3. 开始训练...")
        trainer = CustomTrainer(model, tokenizer, train_dataset, val_dataset)
        trainer.train()
        
        print("\n训练完成！模型已保存到:", config.OUTPUT_DIR)
        
    except Exception as e:
        print(f"\n训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

if __name__ == "__main__":
    main()
