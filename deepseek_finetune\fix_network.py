#!/usr/bin/env python3
"""
网络问题修复脚本 - 设置HuggingFace镜像
"""

import os
import subprocess
import sys

def setup_hf_mirror():
    """设置HuggingFace镜像"""
    print("🔧 设置HuggingFace镜像...")
    
    # 设置环境变量
    mirror_urls = [
        "https://hf-mirror.com",
        "https://huggingface.co",
    ]
    
    for mirror in mirror_urls:
        print(f"尝试镜像: {mirror}")
        os.environ["HF_ENDPOINT"] = mirror
        
        # 测试连接
        try:
            import requests
            response = requests.get(f"{mirror}/api/models", timeout=10)
            if response.status_code == 200:
                print(f"✅ 镜像可用: {mirror}")
                return mirror
        except Exception as e:
            print(f"❌ 镜像不可用: {mirror} - {e}")
            continue
    
    return None

def download_model_manually():
    """手动下载模型"""
    print("📥 手动下载DeepSeek模型...")
    
    # 使用git clone下载
    model_name = "deepseek-ai/deepseek-coder-7b-instruct-v1.5"
    cache_dir = "./cache"
    
    os.makedirs(cache_dir, exist_ok=True)
    
    commands = [
        f"cd {cache_dir}",
        f"git lfs install",
        f"git clone https://hf-mirror.com/{model_name}",
    ]
    
    try:
        for cmd in commands:
            print(f"执行: {cmd}")
            result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
            print(result.stdout)
        
        print("✅ 模型下载完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 下载失败: {e}")
        return False

def update_config_for_local_model():
    """更新配置使用本地模型"""
    print("🔧 更新配置使用本地模型...")
    
    config_file = "config.py"
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换模型路径
    old_model = 'MODEL_NAME = "deepseek-ai/deepseek-coder-7b-instruct-v1.5"'
    new_model = 'MODEL_NAME = "./cache/deepseek-coder-7b-instruct-v1.5"'
    
    content = content.replace(old_model, new_model)
    
    # 写回文件
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 配置已更新为使用本地模型")

def main():
    print("=" * 50)
    print("🌐 HuggingFace网络问题修复")
    print("=" * 50)
    
    # 方案1: 设置镜像
    mirror = setup_hf_mirror()
    if mirror:
        print(f"✅ 使用镜像: {mirror}")
        print("请重新运行训练脚本")
        return
    
    # 方案2: 手动下载
    print("🔄 尝试手动下载模型...")
    if download_model_manually():
        update_config_for_local_model()
        print("✅ 已切换到本地模型，请重新运行训练")
    else:
        print("❌ 所有方案都失败了")
        print("请检查网络连接或联系管理员")

if __name__ == "__main__":
    main()
