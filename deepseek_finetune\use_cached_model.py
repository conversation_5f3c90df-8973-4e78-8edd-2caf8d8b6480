#!/usr/bin/env python3
"""
使用已缓存模型的启动脚本
"""

import os
import sys

def find_cached_model():
    """查找已缓存的模型"""
    possible_paths = [
        "./cache/models/deepseek-ai/deepseek-coder-7b-instruct-v1.5",
        "./cache/models/deepseek-ai/deepseek-coder-7B-instruct-v1.5", 
        "./cache/deepseek-ai--deepseek-coder-7b-instruct-v1.5",
        "./cache/deepseek-ai--deepseek-coder-7B-instruct-v1.5"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到缓存模型: {path}")
            return path
    
    print("❌ 未找到缓存模型")
    return None

def update_config_with_cached_model():
    """更新配置使用缓存模型"""
    cached_path = find_cached_model()
    
    if not cached_path:
        print("请先下载模型或检查缓存路径")
        return False
    
    # 更新config.py
    config_file = "config.py"
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换模型路径
        old_line = 'MODEL_NAME = "deepseek-ai/deepseek-coder-7B-instruct-v1.5"'
        new_line = f'MODEL_NAME = "{cached_path}"'
        
        content = content.replace(old_line, new_line)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新配置使用本地模型: {cached_path}")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")
        return False

def start_training():
    """开始训练"""
    try:
        from main import main as train_main
        train_main()
        return True
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 50)
    print("🚀 使用缓存模型启动训练")
    print("=" * 50)
    
    # 1. 查找并配置缓存模型
    if not update_config_with_cached_model():
        sys.exit(1)
    
    # 2. 开始训练
    print("\n🔥 开始训练（使用本地缓存模型）...")
    
    if start_training():
        print("\n🎉 训练完成！")
    else:
        print("\n❌ 训练失败")

if __name__ == "__main__":
    main()
