"""
训练器模块 - 处理模型训练
"""

import os
import torch
from transformers import (
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq
)
from config import Config

class CustomTrainer:
    def __init__(self, model, tokenizer, train_dataset, val_dataset):
        self.model = model
        self.tokenizer = tokenizer
        self.train_dataset = train_dataset
        self.val_dataset = val_dataset
        self.config = Config()
    
    def create_training_arguments(self):
        """创建训练参数"""
        # 检查transformers版本兼容性
        import transformers
        version = transformers.__version__
        major, minor = map(int, version.split('.')[:2])

        # 根据版本选择参数名
        eval_strategy_key = "eval_strategy" if (major >= 4 and minor >= 21) else "evaluation_strategy"

        # 基础参数
        args_dict = {
            "output_dir": self.config.OUTPUT_DIR,
            "overwrite_output_dir": True,

            # 训练参数
            "num_train_epochs": self.config.NUM_EPOCHS,
            "per_device_train_batch_size": self.config.BATCH_SIZE,
            "per_device_eval_batch_size": self.config.BATCH_SIZE,
            "gradient_accumulation_steps": self.config.GRADIENT_ACCUMULATION_STEPS,
            "learning_rate": self.config.LEARNING_RATE,

            # 优化器和调度器
            "warmup_steps": self.config.WARMUP_STEPS,
            "lr_scheduler_type": "cosine",

            # 保存和日志
            "save_steps": self.config.SAVE_STEPS,
            "save_total_limit": 3,
            "logging_steps": self.config.LOGGING_STEPS,

            # 评估 - 使用兼容的参数名
            eval_strategy_key: "steps",
            "eval_steps": self.config.EVAL_STEPS,

            # 优化设置
            "fp16": self.config.FP16,
            "gradient_checkpointing": self.config.GRADIENT_CHECKPOINTING,
            "dataloader_num_workers": self.config.DATALOADER_NUM_WORKERS,

            # 其他
            "seed": self.config.SEED,
            "data_seed": self.config.SEED,
            "remove_unused_columns": False,

            # 报告
            "report_to": "wandb" if self.config.USE_WANDB else [],
            "run_name": self.config.WANDB_RUN_NAME if self.config.USE_WANDB else None,
        }

        training_args = TrainingArguments(**args_dict)
        return training_args
    
    def create_data_collator(self):
        """创建数据整理器"""
        return DataCollatorForSeq2Seq(
            tokenizer=self.tokenizer,
            model=self.model,
            padding=True,
            max_length=self.config.MAX_LENGTH,
            pad_to_multiple_of=8,  # 优化GPU性能
            return_tensors="pt"
        )
    
    def train(self):
        """开始训练"""
        print("开始训练...")
        
        # 创建训练参数
        training_args = self.create_training_arguments()
        
        # 创建数据整理器
        data_collator = self.create_data_collator()
        
        # 创建trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=self.train_dataset,
            eval_dataset=self.val_dataset,
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )
        
        # 从检查点恢复（如果有）
        resume_from_checkpoint = self.config.RESUME_FROM_CHECKPOINT
        if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
            print(f"从检查点恢复: {resume_from_checkpoint}")
        else:
            resume_from_checkpoint = None
        
        # 开始训练
        trainer.train(resume_from_checkpoint=resume_from_checkpoint)
        
        # 保存最终模型
        trainer.save_model()
        
        print("训练完成！")
        return trainer
