"""
工具函数
"""

import os
import json
import torch
import psutil
from typing import Dict, Any

def get_system_info():
    """获取系统信息"""
    info = {
        "cpu_count": psutil.cpu_count(),
        "memory_total": f"{psutil.virtual_memory().total / 1024**3:.1f} GB",
        "memory_available": f"{psutil.virtual_memory().available / 1024**3:.1f} GB",
    }
    
    if torch.cuda.is_available():
        info["gpu_count"] = torch.cuda.device_count()
        info["gpu_name"] = torch.cuda.get_device_name()
        info["gpu_memory"] = f"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB"
    else:
        info["gpu_count"] = 0
    
    return info

def save_training_config(config, output_dir):
    """保存训练配置"""
    config_dict = {}
    for attr in dir(config):
        if not attr.startswith('_') and not callable(getattr(config, attr)):
            config_dict[attr] = getattr(config, attr)
    
    config_path = os.path.join(output_dir, "training_config.json")
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    print(f"训练配置已保存到: {config_path}")

def estimate_training_time(num_samples, batch_size, num_epochs, steps_per_second=1.0):
    """估算训练时间"""
    total_steps = (num_samples // batch_size) * num_epochs
    estimated_seconds = total_steps / steps_per_second
    
    hours = int(estimated_seconds // 3600)
    minutes = int((estimated_seconds % 3600) // 60)
    
    return f"预估训练时间: {hours}小时{minutes}分钟 (总步数: {total_steps})"

def check_disk_space(path, required_gb=10):
    """检查磁盘空间"""
    free_space = psutil.disk_usage(path).free / 1024**3
    if free_space < required_gb:
        print(f"警告: 磁盘空间不足，当前可用: {free_space:.1f}GB，建议至少: {required_gb}GB")
        return False
    return True

def format_memory_usage():
    """格式化内存使用情况"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        cached = torch.cuda.memory_reserved() / 1024**3
        total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        return f"GPU内存: {allocated:.1f}GB / {total:.1f}GB (缓存: {cached:.1f}GB)"
    return "未使用GPU"

def print_system_status():
    """打印系统状态"""
    print("\n=== 系统状态 ===")
    info = get_system_info()
    for key, value in info.items():
        print(f"{key}: {value}")
    print(f"内存使用: {format_memory_usage()}")
    print("=" * 20)
