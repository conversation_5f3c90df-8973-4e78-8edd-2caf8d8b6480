{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 3.0, "eval_steps": 500, "global_step": 30, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "grad_norm": 0.20611846446990967, "learning_rate": 3.6e-05, "loss": 1.0646, "step": 10}, {"epoch": 2.0, "grad_norm": 0.25509583950042725, "learning_rate": 7.6e-05, "loss": 0.9449, "step": 20}, {"epoch": 3.0, "grad_norm": 0.16655661165714264, "learning_rate": 0.000116, "loss": 0.7392, "step": 30}], "logging_steps": 10, "max_steps": 30, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1.4960617280372736e+16, "train_batch_size": 1, "trial_name": null, "trial_params": null}