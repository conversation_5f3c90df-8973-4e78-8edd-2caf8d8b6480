#!/usr/bin/env python3
"""
修复模型路径问题 - 自动检测正确的模型路径
"""

import os
import glob

def find_model_directory():
    """查找实际的模型目录"""
    print("🔍 查找模型目录...")
    
    # 可能的路径模式
    patterns = [
        "./cache/models/deepseek-ai/deepseek-coder-7b-instruct-v1.5",
        "./cache/models/deepseek-ai/deepseek-coder-7B-instruct-v1.5",
        "./cache/models/deepseek-ai/*deepseek-coder*",
        "./cache/deepseek-ai--*",
        "./cache/*deepseek*"
    ]
    
    for pattern in patterns:
        matches = glob.glob(pattern)
        for match in matches:
            if os.path.isdir(match):
                # 检查是否包含模型文件
                model_files = glob.glob(os.path.join(match, "*.safetensors"))
                config_file = os.path.join(match, "config.json")
                
                if model_files and os.path.exists(config_file):
                    print(f"✅ 找到完整模型: {match}")
                    print(f"   模型文件数量: {len(model_files)}")
                    return match
                else:
                    print(f"⚠️  目录存在但不完整: {match}")
    
    print("❌ 未找到完整的模型目录")
    return None

def list_cache_structure():
    """列出缓存目录结构"""
    print("\n📁 缓存目录结构:")
    
    cache_dir = "./cache"
    if not os.path.exists(cache_dir):
        print("❌ 缓存目录不存在")
        return
    
    for root, dirs, files in os.walk(cache_dir):
        level = root.replace(cache_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        
        # 只显示前几个文件，避免输出太长
        subindent = ' ' * 2 * (level + 1)
        for i, file in enumerate(files[:5]):
            print(f"{subindent}{file}")
        if len(files) > 5:
            print(f"{subindent}... 还有 {len(files) - 5} 个文件")

def update_config_with_correct_path(model_path):
    """更新配置文件使用正确路径"""
    config_file = "config.py"
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换MODEL_NAME
        import re
        pattern = r'MODEL_NAME = ["\'].*?["\']'
        new_line = f'MODEL_NAME = "{model_path}"'
        
        content = re.sub(pattern, new_line, content)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新配置文件: {model_path}")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 修复模型路径问题")
    print("=" * 60)
    
    # 1. 显示缓存结构
    list_cache_structure()
    
    # 2. 查找正确的模型路径
    model_path = find_model_directory()
    
    if model_path:
        # 3. 更新配置
        if update_config_with_correct_path(model_path):
            print("\n✅ 修复完成！现在可以重新开始训练")
            print("运行: python restart_training.py")
        else:
            print("\n❌ 配置更新失败")
    else:
        print("\n❌ 未找到可用的模型")
        print("可能需要重新下载模型")
        
        # 提供重新下载的选项
        print("\n🔄 重新下载模型:")
        print("python start_modelscope.py")

if __name__ == "__main__":
    main()
