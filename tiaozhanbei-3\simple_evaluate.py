import json
import re
from collections import Counter, defaultdict
import random

def load_dataset(file_path):
    """加载JSONL数据集"""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    item = json.loads(line.strip())
                    data.append(item)
                except json.JSONDecodeError as e:
                    print(f"第{line_num}行JSON解析错误: {e}")
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
        return []
    
    print(f"✅ 成功加载 {len(data)} 条数据")
    return data

def basic_statistics(data):
    """基础统计分析"""
    print("\n" + "="*60)
    print("📊 基础统计分析")
    print("="*60)
    
    # 数据完整性检查
    complete_data = 0
    missing_fields = defaultdict(int)
    
    for item in data:
        if all(key in item and item[key].strip() for key in ['query', 'context', 'response']):
            complete_data += 1
        else:
            for key in ['query', 'context', 'response']:
                if key not in item or not item[key].strip():
                    missing_fields[key] += 1
    
    print(f"📈 数据总量: {len(data)} 条")
    print(f"✅ 完整数据: {complete_data} 条 ({complete_data/len(data)*100:.1f}%)")
    
    if missing_fields:
        print("❌ 缺失字段统计:")
        for field, count in missing_fields.items():
            print(f"   {field}: {count} 条")
    
    # 长度统计
    query_lengths = [len(item['query']) for item in data if 'query' in item]
    context_lengths = [len(item['context']) for item in data if 'context' in item]
    response_lengths = [len(item['response']) for item in data if 'response' in item]
    
    print(f"\n📏 长度统计:")
    if query_lengths:
        print(f"问题长度 - 平均: {sum(query_lengths)/len(query_lengths):.1f}, 最短: {min(query_lengths)}, 最长: {max(query_lengths)}")
    if context_lengths:
        print(f"上下文长度 - 平均: {sum(context_lengths)/len(context_lengths):.1f}, 最短: {min(context_lengths)}, 最长: {max(context_lengths)}")
    if response_lengths:
        print(f"答案长度 - 平均: {sum(response_lengths)/len(response_lengths):.1f}, 最短: {min(response_lengths)}, 最长: {max(response_lengths)}")
    
    return complete_data/len(data), query_lengths, response_lengths

def content_analysis(data):
    """内容质量分析"""
    print("\n" + "="*60)
    print("📝 内容质量分析")
    print("="*60)
    
    # 数据类型分布
    data_types = {"时序数据": 0, "法规文档": 0, "其他": 0}
    question_patterns = defaultdict(int)
    
    for item in data:
        context = item.get('context', '')
        query = item.get('query', '')
        
        # 数据类型分类
        if "时间:" in context and "闸前水位" in context:
            data_types["时序数据"] += 1
        elif any(keyword in context for keyword in ["南水北调", "管理", "条例", "规定", "办法"]):
            data_types["法规文档"] += 1
        else:
            data_types["其他"] += 1
        
        # 问题类型分析
        if any(word in query for word in ["最大", "最小", "平均", "趋势", "变化"]):
            question_patterns["数据分析类"] += 1
        elif any(word in query for word in ["时间", "期间", "什么时候", "何时"]):
            question_patterns["时间查询类"] += 1
        elif any(word in query for word in ["如何", "怎样", "流程", "步骤", "方法"]):
            question_patterns["流程询问类"] += 1
        elif any(word in query for word in ["是否", "有没有", "能否", "可以"]):
            question_patterns["判断类"] += 1
        elif any(word in query for word in ["什么", "哪些", "包括", "含义"]):
            question_patterns["概念解释类"] += 1
        else:
            question_patterns["其他类型"] += 1
    
    print("📊 数据类型分布:")
    for dtype, count in data_types.items():
        print(f"   {dtype}: {count} 条 ({count/len(data)*100:.1f}%)")
    
    print("\n🔍 问题类型分布:")
    for qtype, count in question_patterns.items():
        print(f"   {qtype}: {count} 条 ({count/len(data)*100:.1f}%)")
    
    # 计算多样性得分
    diversity_score = len(question_patterns) * 10  # 类型越多样性越高
    balance_score = 100 - max(question_patterns.values()) / len(data) * 100  # 分布越均匀越好
    
    return data_types, question_patterns, (diversity_score + balance_score) / 2

def quality_checks(data):
    """质量检查"""
    print("\n" + "="*60)
    print("🔍 质量检查")
    print("="*60)
    
    issues = []
    duplicates = set()
    
    for i, item in enumerate(data):
        query = item.get('query', '')
        response = item.get('response', '')
        context = item.get('context', '')
        
        # 检查1: 问题格式
        if query:
            if not query.rstrip().endswith(('?', '？')):
                if not any(word in query for word in ['吗', '呢', '如何', '怎样', '什么', '哪些', '是否']):
                    issues.append(f"第{i+1}条: 问题可能不是疑问句")
        
        # 检查2: 答案长度
        if len(response) < 15:
            issues.append(f"第{i+1}条: 答案过短 ({len(response)}字符)")
        elif len(response) > 500:
            issues.append(f"第{i+1}条: 答案过长 ({len(response)}字符)")
        
        # 检查3: 重复问题
        if query in duplicates:
            issues.append(f"第{i+1}条: 问题重复")
        else:
            duplicates.add(query)
        
        # 检查4: 答案质量
        if response:
            if response.count('根据') > 3:
                issues.append(f"第{i+1}条: 答案可能过于模板化")
            if len(response.split('。')) < 2:
                issues.append(f"第{i+1}条: 答案可能不够详细")
    
    print(f"🔍 发现 {len(issues)} 个潜在问题:")
    for issue in issues[:15]:  # 显示前15个问题
        print(f"   ⚠️ {issue}")
    
    if len(issues) > 15:
        print(f"   ... 还有 {len(issues)-15} 个问题")
    
    # 计算质量得分
    quality_score = max(0, 100 - len(issues) / len(data) * 50)
    
    return issues, quality_score

def generate_samples(data, n=5):
    """生成数据样本"""
    print("\n" + "="*60)
    print(f"📝 数据样本展示 (随机{n}条)")
    print("="*60)
    
    samples = random.sample(data, min(n, len(data)))
    
    for i, item in enumerate(samples, 1):
        print(f"\n【样本 {i}】")
        print(f"问题: {item.get('query', 'N/A')}")
        print(f"答案: {item.get('response', 'N/A')[:150]}{'...' if len(item.get('response', '')) > 150 else ''}")
        
        # 判断数据类型
        context = item.get('context', '')
        if "时间:" in context and "闸前水位" in context:
            data_type = "时序数据"
        elif any(keyword in context for keyword in ["南水北调", "管理", "条例"]):
            data_type = "法规文档"
        else:
            data_type = "其他"
        print(f"类型: {data_type}")
        print("-" * 50)

def professional_evaluation(data):
    """专业性评估"""
    print("\n" + "="*60)
    print("🎓 专业性评估")
    print("="*60)
    
    # 专业词汇统计
    water_terms = ['水位', '流量', '闸门', '开度', '渡槽', '节制闸', '南水北调', '工程', '管理', '调度']
    technical_terms = ['立方米', 'm³/s', 'mm', '米', '时序', '数据', '监测', '运行']
    
    professional_score = 0
    total_items = len(data)
    
    for item in data:
        text = item.get('query', '') + item.get('response', '') + item.get('context', '')
        
        # 计算专业词汇密度
        water_count = sum(1 for term in water_terms if term in text)
        tech_count = sum(1 for term in technical_terms if term in text)
        
        if water_count >= 2 or tech_count >= 1:
            professional_score += 1
    
    professional_rate = professional_score / total_items * 100
    
    print(f"💧 水利专业词汇覆盖率: {professional_rate:.1f}%")
    
    # 数值准确性检查
    numeric_accuracy = 0
    numeric_total = 0
    
    for item in data:
        response = item.get('response', '')
        if re.search(r'\d+\.?\d*', response):  # 包含数字
            numeric_total += 1
            if any(unit in response for unit in ['m³/s', 'mm', '米', '立方米']):
                numeric_accuracy += 1
    
    if numeric_total > 0:
        accuracy_rate = numeric_accuracy / numeric_total * 100
        print(f"📊 数值答案单位准确率: {accuracy_rate:.1f}%")
    else:
        accuracy_rate = 0
        print(f"📊 数值答案单位准确率: 无数值答案")
    
    return (professional_rate + accuracy_rate) / 2

def main():
    """主评估函数"""
    file_path = "enhanced_rag_dataset.jsonl"
    
    print("🔍 开始评估数据集质量...")
    print("📁 文件:", file_path)
    
    # 加载数据
    data = load_dataset(file_path)
    if not data:
        return
    
    # 各项评估
    completeness_rate, query_lengths, response_lengths = basic_statistics(data)
    data_types, question_patterns, diversity_score = content_analysis(data)
    issues, quality_score = quality_checks(data)
    professional_score = professional_evaluation(data)
    
    # 样本展示
    generate_samples(data)
    
    # 总体评分
    print("\n" + "="*60)
    print("🏆 总体质量评分")
    print("="*60)
    
    # 计算各项得分
    completeness_score = completeness_rate * 100
    
    total_score = (completeness_score + diversity_score + quality_score + professional_score) / 4
    
    print(f"📊 完整性得分: {completeness_score:.1f}/100")
    print(f"🌈 多样性得分: {diversity_score:.1f}/100")
    print(f"🔍 质量得分: {quality_score:.1f}/100")
    print(f"🎓 专业性得分: {professional_score:.1f}/100")
    print(f"🏆 总体得分: {total_score:.1f}/100")
    
    # 评级
    if total_score >= 90:
        grade = "A+ (优秀)"
        emoji = "🌟"
    elif total_score >= 80:
        grade = "A (良好)"
        emoji = "✨"
    elif total_score >= 70:
        grade = "B (中等)"
        emoji = "👍"
    elif total_score >= 60:
        grade = "C (及格)"
        emoji = "👌"
    else:
        grade = "D (需要改进)"
        emoji = "⚠️"
    
    print(f"{emoji} 数据集评级: {grade}")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    if completeness_score < 95:
        print("   - 检查并修复缺失字段")
    if diversity_score < 70:
        print("   - 增加问题类型多样性")
    if quality_score < 80:
        print("   - 修复质量问题，优化问答格式")
    if professional_score < 80:
        print("   - 增强专业术语使用，提高技术准确性")
    
    if total_score >= 80:
        print("   🎉 数据集质量良好，可以用于微调训练！")

if __name__ == "__main__":
    main()
