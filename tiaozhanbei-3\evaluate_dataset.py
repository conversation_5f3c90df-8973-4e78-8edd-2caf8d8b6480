import json
import re
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
import pandas as pd
import numpy as np
from textstat import flesch_reading_ease, flesch_kincaid_grade
import jieba

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_dataset(file_path):
    """加载JSONL数据集"""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    item = json.loads(line.strip())
                    data.append(item)
                except json.JSONDecodeError as e:
                    print(f"第{line_num}行JSON解析错误: {e}")
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
        return []
    
    print(f"✅ 成功加载 {len(data)} 条数据")
    return data

def basic_statistics(data):
    """基础统计分析"""
    print("\n" + "="*50)
    print("📊 基础统计分析")
    print("="*50)
    
    # 数据完整性检查
    complete_data = 0
    missing_fields = defaultdict(int)
    
    for item in data:
        if all(key in item and item[key].strip() for key in ['query', 'context', 'response']):
            complete_data += 1
        else:
            for key in ['query', 'context', 'response']:
                if key not in item or not item[key].strip():
                    missing_fields[key] += 1
    
    print(f"📈 数据总量: {len(data)} 条")
    print(f"✅ 完整数据: {complete_data} 条 ({complete_data/len(data)*100:.1f}%)")
    
    if missing_fields:
        print("❌ 缺失字段统计:")
        for field, count in missing_fields.items():
            print(f"   {field}: {count} 条")
    
    # 长度统计
    query_lengths = [len(item['query']) for item in data if 'query' in item]
    context_lengths = [len(item['context']) for item in data if 'context' in item]
    response_lengths = [len(item['response']) for item in data if 'response' in item]
    
    print(f"\n📏 长度统计:")
    print(f"问题长度 - 平均: {np.mean(query_lengths):.1f}, 中位数: {np.median(query_lengths):.1f}, 范围: {min(query_lengths)}-{max(query_lengths)}")
    print(f"上下文长度 - 平均: {np.mean(context_lengths):.1f}, 中位数: {np.median(context_lengths):.1f}, 范围: {min(context_lengths)}-{max(context_lengths)}")
    print(f"答案长度 - 平均: {np.mean(response_lengths):.1f}, 中位数: {np.median(response_lengths):.1f}, 范围: {min(response_lengths)}-{max(response_lengths)}")
    
    return {
        'complete_rate': complete_data/len(data),
        'query_lengths': query_lengths,
        'context_lengths': context_lengths,
        'response_lengths': response_lengths
    }

def content_analysis(data):
    """内容质量分析"""
    print("\n" + "="*50)
    print("📝 内容质量分析")
    print("="*50)
    
    # 数据类型分布
    data_types = {"时序数据": 0, "法规文档": 0, "其他": 0}
    question_patterns = defaultdict(int)
    
    for item in data:
        context = item.get('context', '')
        query = item.get('query', '')
        
        # 数据类型分类
        if "时间:" in context and "闸前水位" in context:
            data_types["时序数据"] += 1
        elif any(keyword in context for keyword in ["南水北调", "管理", "条例", "规定", "办法"]):
            data_types["法规文档"] += 1
        else:
            data_types["其他"] += 1
        
        # 问题类型分析
        if any(word in query for word in ["最大", "最小", "平均", "趋势"]):
            question_patterns["数据分析类"] += 1
        elif any(word in query for word in ["时间", "期间", "什么时候"]):
            question_patterns["时间查询类"] += 1
        elif any(word in query for word in ["如何", "怎样", "流程", "步骤"]):
            question_patterns["流程询问类"] += 1
        elif any(word in query for word in ["是否", "有没有", "能否"]):
            question_patterns["判断类"] += 1
        elif any(word in query for word in ["什么", "哪些", "包括"]):
            question_patterns["概念解释类"] += 1
        else:
            question_patterns["其他类型"] += 1
    
    print("📊 数据类型分布:")
    for dtype, count in data_types.items():
        print(f"   {dtype}: {count} 条 ({count/len(data)*100:.1f}%)")
    
    print("\n🔍 问题类型分布:")
    for qtype, count in question_patterns.items():
        print(f"   {qtype}: {count} 条 ({count/len(data)*100:.1f}%)")
    
    return data_types, question_patterns

def quality_checks(data):
    """质量检查"""
    print("\n" + "="*50)
    print("🔍 质量检查")
    print("="*50)
    
    issues = []
    
    for i, item in enumerate(data):
        query = item.get('query', '')
        response = item.get('response', '')
        context = item.get('context', '')
        
        # 检查1: 问题是否以问号结尾
        if query and not query.rstrip().endswith(('?', '？', '吗', '呢')):
            issues.append(f"第{i+1}条: 问题可能不是疑问句")
        
        # 检查2: 答案是否过短
        if len(response) < 10:
            issues.append(f"第{i+1}条: 答案过短 ({len(response)}字符)")
        
        # 检查3: 答案是否与问题相关
        if query and response:
            # 简单的关键词匹配检查
            query_words = set(jieba.cut(query))
            response_words = set(jieba.cut(response))
            common_words = query_words & response_words
            if len(common_words) < 2:
                issues.append(f"第{i+1}条: 问答相关性可能较低")
        
        # 检查4: 重复内容
        if i > 0:
            for j in range(i):
                if data[j]['query'] == query:
                    issues.append(f"第{i+1}条: 问题与第{j+1}条重复")
                    break
    
    print(f"🔍 发现 {len(issues)} 个潜在问题:")
    for issue in issues[:10]:  # 只显示前10个问题
        print(f"   ⚠️ {issue}")
    
    if len(issues) > 10:
        print(f"   ... 还有 {len(issues)-10} 个问题")
    
    return issues

def diversity_analysis(data):
    """多样性分析"""
    print("\n" + "="*50)
    print("🌈 多样性分析")
    print("="*50)
    
    # 词汇多样性
    all_queries = ' '.join([item.get('query', '') for item in data])
    all_responses = ' '.join([item.get('response', '') for item in data])
    
    query_words = list(jieba.cut(all_queries))
    response_words = list(jieba.cut(all_responses))
    
    # 去除停用词和标点
    stop_words = {'的', '了', '在', '是', '和', '与', '或', '等', '、', '，', '。', '？', '！'}
    query_words = [w for w in query_words if len(w) > 1 and w not in stop_words]
    response_words = [w for w in response_words if len(w) > 1 and w not in stop_words]
    
    query_vocab = set(query_words)
    response_vocab = set(response_words)
    
    print(f"📚 词汇统计:")
    print(f"   问题词汇量: {len(query_vocab)} 个独特词汇")
    print(f"   答案词汇量: {len(response_vocab)} 个独特词汇")
    print(f"   问题词汇多样性: {len(query_vocab)/len(query_words):.3f}")
    print(f"   答案词汇多样性: {len(response_vocab)/len(response_words):.3f}")
    
    # 高频词分析
    query_freq = Counter(query_words)
    response_freq = Counter(response_words)
    
    print(f"\n🔥 问题高频词 (Top 10):")
    for word, freq in query_freq.most_common(10):
        print(f"   {word}: {freq} 次")
    
    print(f"\n🔥 答案高频词 (Top 10):")
    for word, freq in response_freq.most_common(10):
        print(f"   {word}: {freq} 次")

def generate_samples(data, n=5):
    """生成数据样本"""
    print("\n" + "="*50)
    print(f"📝 数据样本展示 (随机{n}条)")
    print("="*50)
    
    import random
    samples = random.sample(data, min(n, len(data)))
    
    for i, item in enumerate(samples, 1):
        print(f"\n【样本 {i}】")
        print(f"问题: {item.get('query', 'N/A')}")
        print(f"答案: {item.get('response', 'N/A')}")
        print(f"上下文: {item.get('context', 'N/A')[:100]}...")
        print("-" * 50)

def main():
    """主评估函数"""
    file_path = "high_quality_rag_dataset_500.jsonl"
    
    print("🔍 开始评估数据集质量...")
    
    # 加载数据
    data = load_dataset(file_path)
    if not data:
        return
    
    # 基础统计
    stats = basic_statistics(data)
    
    # 内容分析
    data_types, question_patterns = content_analysis(data)
    
    # 质量检查
    issues = quality_checks(data)
    
    # 多样性分析
    diversity_analysis(data)
    
    # 样本展示
    generate_samples(data)
    
    # 总体评分
    print("\n" + "="*50)
    print("🏆 总体质量评分")
    print("="*50)
    
    # 计算各项得分
    completeness_score = stats['complete_rate'] * 100
    diversity_score = min(100, len(set([item['query'] for item in data])) / len(data) * 100)
    quality_score = max(0, 100 - len(issues) / len(data) * 100)
    
    total_score = (completeness_score + diversity_score + quality_score) / 3
    
    print(f"📊 完整性得分: {completeness_score:.1f}/100")
    print(f"🌈 多样性得分: {diversity_score:.1f}/100")
    print(f"🔍 质量得分: {quality_score:.1f}/100")
    print(f"🏆 总体得分: {total_score:.1f}/100")
    
    # 评级
    if total_score >= 90:
        grade = "A+ (优秀)"
    elif total_score >= 80:
        grade = "A (良好)"
    elif total_score >= 70:
        grade = "B (中等)"
    elif total_score >= 60:
        grade = "C (及格)"
    else:
        grade = "D (需要改进)"
    
    print(f"📈 数据集评级: {grade}")

if __name__ == "__main__":
    main()
