#!/usr/bin/env python3
"""
简化的训练启动脚本
"""

import os
import sys

def main():
    print("=" * 60)
    print("🚀 DeepSeek 水利工程模型微调")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("config.py"):
        print("❌ 错误: 请在 deepseek_finetune 目录下运行此脚本")
        sys.exit(1)
    
    # 检查数据文件
    from config import Config
    config = Config()
    
    if not os.path.exists(config.DATA_PATH):
        print(f"❌ 错误: 找不到训练数据文件: {config.DATA_PATH}")
        print("请检查数据文件路径是否正确")
        sys.exit(1)
    
    print(f"✅ 找到训练数据: {config.DATA_PATH}")
    
    # 开始训练
    print("\n🔥 开始训练...")
    try:
        from main import main as train_main
        train_main()
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
