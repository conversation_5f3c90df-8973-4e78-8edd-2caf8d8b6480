#!/usr/bin/env python3
"""
重新启动训练 - 修复数据长度问题
"""

import os
import sys

def set_environment():
    """设置环境变量"""
    # 解决tokenizer并行警告
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    
    # 设置CUDA
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    
    # ModelScope缓存
    os.environ["MODELSCOPE_CACHE"] = "./cache"
    
    print("✅ 环境变量设置完成")

def start_training():
    """开始训练"""
    print("🚀 重新开始训练...")
    print("已修复数据长度不一致问题")
    print("-" * 40)
    
    try:
        from main import main as train_main
        train_main()
        return True
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 50)
    print("🔧 重新启动训练（已修复数据问题）")
    print("=" * 50)
    
    # 1. 设置环境
    set_environment()
    
    # 2. 开始训练
    if start_training():
        print("\n🎉 训练完成！")
        print("模型保存在: ./output/")
        print("测试模型: python inference.py")
    else:
        print("\n❌ 训练失败")

if __name__ == "__main__":
    main()
