#!/usr/bin/env python3
"""
PyTorch安装脚本 - 自动检测CUDA版本并安装合适的PyTorch
"""

import subprocess
import sys
import re

def run_command(cmd):
    """运行命令并返回输出"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def get_cuda_version():
    """检测CUDA版本"""
    print("检测CUDA版本...")
    
    # 尝试nvidia-smi
    success, stdout, stderr = run_command("nvidia-smi")
    if success and "CUDA Version" in stdout:
        # 提取CUDA版本
        match = re.search(r"CUDA Version: (\d+\.\d+)", stdout)
        if match:
            version = match.group(1)
            print(f"检测到CUDA版本: {version}")
            return version
    
    # 尝试nvcc
    success, stdout, stderr = run_command("nvcc --version")
    if success:
        match = re.search(r"release (\d+\.\d+)", stdout)
        if match:
            version = match.group(1)
            print(f"检测到CUDA版本: {version}")
            return version
    
    print("未检测到CUDA，将安装CPU版本")
    return None

def install_pytorch(cuda_version=None):
    """安装PyTorch"""
    if cuda_version is None:
        # CPU版本
        cmd = "pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2"
        print("安装CPU版本PyTorch...")
    elif cuda_version.startswith("11."):
        # CUDA 11.x
        cmd = "pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 --index-url https://download.pytorch.org/whl/cu118"
        print("安装CUDA 11.8版本PyTorch...")
    elif cuda_version.startswith("12."):
        # CUDA 12.x
        cmd = "pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 --index-url https://download.pytorch.org/whl/cu121"
        print("安装CUDA 12.1版本PyTorch...")
    else:
        # 默认最新版本
        cmd = "pip install torch torchvision torchaudio"
        print("安装默认版本PyTorch...")
    
    print(f"执行命令: {cmd}")
    success, stdout, stderr = run_command(cmd)
    
    if success:
        print("✅ PyTorch安装成功！")
        return True
    else:
        print(f"❌ PyTorch安装失败: {stderr}")
        return False

def install_other_packages():
    """安装其他依赖包"""
    print("安装其他依赖包...")
    
    packages = [
        "transformers>=4.36.0",
        "peft>=0.7.0", 
        "datasets>=2.14.0",
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0",
        "tqdm",
        "numpy",
        "pandas",
        "psutil"
    ]
    
    for package in packages:
        print(f"安装 {package}...")
        success, stdout, stderr = run_command(f"pip install {package}")
        if not success:
            print(f"警告: {package} 安装失败")
    
    print("✅ 依赖包安装完成！")

def verify_installation():
    """验证安装"""
    print("验证PyTorch安装...")
    
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.cuda.get_device_name()}")
            print(f"✅ CUDA版本: {torch.version.cuda}")
        else:
            print("⚠️  CUDA不可用，将使用CPU")
        
        return True
    except ImportError:
        print("❌ PyTorch导入失败")
        return False

def main():
    print("=" * 50)
    print("🚀 PyTorch自动安装脚本")
    print("=" * 50)
    
    # 1. 检测CUDA版本
    cuda_version = get_cuda_version()
    
    # 2. 安装PyTorch
    if not install_pytorch(cuda_version):
        print("PyTorch安装失败，请手动安装")
        sys.exit(1)
    
    # 3. 安装其他依赖
    install_other_packages()
    
    # 4. 验证安装
    if verify_installation():
        print("\n🎉 所有依赖安装完成！现在可以开始训练了")
        print("运行: python start_training.py")
    else:
        print("\n❌ 安装验证失败，请检查错误信息")

if __name__ == "__main__":
    main()
