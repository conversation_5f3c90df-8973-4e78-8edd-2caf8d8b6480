{"add_bos_token": true, "add_eos_token": false, "add_prefix_space": null, "added_tokens_decoder": {"100000": {"content": "<｜begin▁of▁sentence｜>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}, "100001": {"content": "<｜end▁of▁sentence｜>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}, "100002": {"content": "ø", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100003": {"content": "ö", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100004": {"content": "ú", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100005": {"content": "ÿ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100006": {"content": "õ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100007": {"content": "÷", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100008": {"content": "û", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100009": {"content": "ý", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100010": {"content": "À", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100011": {"content": "ù", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100012": {"content": "Á", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100013": {"content": "þ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100014": {"content": "ü", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "100015": {"content": "<|EOT|>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}}, "bos_token": "<｜begin▁of▁sentence｜>", "clean_up_tokenization_spaces": false, "eos_token": "<|EOT|>", "extra_special_tokens": {}, "legacy": true, "model_max_length": 4096, "pad_token": "<｜end▁of▁sentence｜>", "sp_model_kwargs": {}, "tokenizer_class": "LlamaTokenizerFast", "unk_token": null, "use_default_system_prompt": false}