# DeepSeek 水利工程模型微调

这是一个基于DeepSeek-Coder-7B的水利工程专业模型微调项目。

## 项目结构

```
deepseek_finetune/
├── config.py          # 配置文件
├── data_processor.py   # 数据处理
├── model_setup.py      # 模型设置
├── trainer.py          # 训练器
├── main.py            # 主训练脚本
├── inference.py       # 推理脚本
├── requirements.txt   # 依赖包
├── run.sh            # 运行脚本
└── README.md         # 说明文档
```

## 环境要求

- Python 3.8+
- CUDA 11.8+
- GPU内存: 至少24GB（推荐RTX 4090或A100）

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置参数

编辑 `config.py` 文件，主要配置：

- `DATA_PATH`: 训练数据路径
- `MODEL_NAME`: 基础模型名称
- `BATCH_SIZE`: 批次大小（根据显存调整）
- `NUM_EPOCHS`: 训练轮数

### 3. 开始训练

```bash
# 方法1: 使用脚本
chmod +x run.sh
./run.sh

# 方法2: 直接运行
python main.py
```

### 4. 测试模型

```bash
# 交互式测试
python inference.py

# 运行预设测试
python inference.py test
```

## 配置说明

### 模型配置
- 默认使用 `deepseek-coder-7b-instruct-v1.5`
- 可以改为 `deepseek-llm-7b-chat` 用于通用对话

### LoRA配置
- rank: 16 (可调整为8-32)
- alpha: 32
- dropout: 0.1

### 训练配置
- 学习率: 2e-4
- 批次大小: 4
- 梯度累积: 4步
- 训练轮数: 3轮

## 显存优化

如果显存不够，可以：

1. 减小 `BATCH_SIZE`
2. 启用量化：在 `main.py` 中设置 `use_quantization=True`
3. 减小 `MAX_LENGTH`
4. 减小 `LORA_R`

## 监控训练

### 使用wandb（可选）
在 `config.py` 中设置：
```python
USE_WANDB = True
WANDB_PROJECT = "your-project-name"
```

### 查看日志
训练日志会保存在 `./output/` 目录下

## 常见问题

### 1. 显存不足
- 减小batch_size
- 启用量化
- 使用gradient_checkpointing

### 2. 训练速度慢
- 检查是否使用了GPU
- 增加dataloader_num_workers
- 使用fp16训练

### 3. 模型效果不好
- 增加训练数据
- 调整学习率
- 增加训练轮数

## 输出文件

训练完成后，在 `./output/` 目录下会有：
- `adapter_config.json`: LoRA配置
- `adapter_model.bin`: LoRA权重
- `training_args.bin`: 训练参数
- 检查点文件夹

## 部署使用

微调完成后，可以使用 `inference.py` 进行推理，或者将模型集成到您的应用中。
