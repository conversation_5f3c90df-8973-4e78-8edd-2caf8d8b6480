#!/usr/bin/env python3
"""
显存优化启动脚本 - 解决CUDA OOM问题
"""

import os
import sys
import torch

def optimize_memory():
    """优化显存设置"""
    print("🔧 优化显存设置...")
    
    # 清理GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("✅ 清理GPU缓存")
    
    # 设置环境变量
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"
    
    # 设置CUDA内存分配策略
    if torch.cuda.is_available():
        torch.cuda.set_per_process_memory_fraction(0.95)  # 使用95%显存
    
    print("✅ 显存优化完成")

def check_memory_usage():
    """检查显存使用情况"""
    if torch.cuda.is_available():
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        allocated = torch.cuda.memory_allocated() / 1024**3
        cached = torch.cuda.memory_reserved() / 1024**3
        
        print(f"📊 显存状态:")
        print(f"   总显存: {total_memory:.1f} GB")
        print(f"   已分配: {allocated:.1f} GB")
        print(f"   已缓存: {cached:.1f} GB")
        print(f"   可用: {total_memory - cached:.1f} GB")

def start_training():
    """开始训练"""
    print("🚀 开始显存优化训练...")
    print("配置: batch_size=1, 4bit量化, 梯度检查点")
    print("-" * 50)
    
    try:
        from main import main as train_main
        train_main()
        return True
    except torch.cuda.OutOfMemoryError as e:
        print(f"❌ 显存不足: {e}")
        print("\n💡 建议:")
        print("1. 进一步减小MAX_LENGTH")
        print("2. 使用更小的模型")
        print("3. 减少LoRA rank")
        return False
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 60)
    print("🎯 显存优化训练启动器")
    print("解决CUDA Out of Memory问题")
    print("=" * 60)
    
    # 1. 优化显存
    optimize_memory()
    
    # 2. 检查显存状态
    check_memory_usage()
    
    # 3. 开始训练
    if start_training():
        print("\n🎉 训练完成！")
        print("模型保存在: ./output/")
    else:
        print("\n❌ 训练失败")
        print("请检查显存使用情况")

if __name__ == "__main__":
    main()
