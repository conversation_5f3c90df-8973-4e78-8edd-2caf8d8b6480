#!/usr/bin/env python3
"""
RTX 4090专用启动脚本 - 针对您的服务器配置优化
"""

import os
import sys
import subprocess

def check_environment():
    """检查环境"""
    print("🔍 检查环境配置...")
    
    # 检查Python版本
    python_version = sys.version
    print(f"Python版本: {python_version}")
    
    # 检查PyTorch
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.version.cuda}")
            print(f"✅ GPU: {torch.cuda.get_device_name()}")
            memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ GPU内存: {memory_gb:.1f} GB")
            
            if memory_gb >= 20:
                print("🚀 显存充足，将使用高性能配置")
                return True
            else:
                print("⚠️  显存可能不足")
                return False
        else:
            print("❌ CUDA不可用")
            return False
    except ImportError:
        print("❌ PyTorch未安装")
        return False

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    
    # 核心依赖
    packages = [
        "transformers>=4.36.0",
        "peft>=0.7.0",
        "datasets>=2.14.0", 
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0",
        "tqdm",
        "numpy",
        "pandas",
        "psutil"
    ]
    
    for package in packages:
        print(f"安装 {package}...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"⚠️  {package} 安装失败，继续...")
    
    print("✅ 依赖安装完成")

def optimize_for_rtx4090():
    """为RTX 4090优化配置"""
    print("⚡ 为RTX 4090优化配置...")
    
    # 设置环境变量
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    
    # 优化CUDA设置
    os.environ["CUDA_LAUNCH_BLOCKING"] = "0"  # 异步执行
    os.environ["TORCH_CUDNN_V8_API_ENABLED"] = "1"  # 启用cuDNN v8
    
    print("✅ 优化配置完成")

def start_training():
    """开始训练"""
    print("🚀 开始训练...")
    
    # 检查数据文件
    if not os.path.exists("./finetune_data/train_alpaca.jsonl"):
        print("❌ 找不到训练数据文件")
        return False
    
    try:
        # 导入并运行训练
        from main import main as train_main
        train_main()
        return True
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 60)
    print("🎯 RTX 4090 DeepSeek微调专用启动器")
    print("配置: PyTorch 2.3.0 + CUDA 12.1 + RTX 4090 24GB")
    print("=" * 60)
    
    # 1. 检查环境
    if not check_environment():
        print("❌ 环境检查失败")
        sys.exit(1)
    
    # 2. 安装依赖
    install_dependencies()
    
    # 3. 优化配置
    optimize_for_rtx4090()
    
    # 4. 开始训练
    print("\n🔥 准备开始训练...")
    print("预计训练时间: 30-60分钟")
    print("显存使用: 约18-20GB")
    print("-" * 40)
    
    if start_training():
        print("\n🎉 训练完成！")
        print("模型保存在: ./output/")
        print("测试模型: python inference.py")
    else:
        print("\n❌ 训练失败")

if __name__ == "__main__":
    main()
