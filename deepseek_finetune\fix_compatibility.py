#!/usr/bin/env python3
"""
兼容性修复脚本 - 检查并修复transformers版本兼容性问题
"""

import sys

def check_transformers_version():
    """检查transformers版本"""
    try:
        import transformers
        version = transformers.__version__
        print(f"Transformers版本: {version}")
        
        # 解析版本号
        major, minor = map(int, version.split('.')[:2])
        
        if major >= 4 and minor >= 21:
            print("✅ 版本兼容，使用 eval_strategy")
            return "new"
        else:
            print("⚠️  旧版本，使用 evaluation_strategy")
            return "old"
            
    except ImportError:
        print("❌ transformers未安装")
        return None

def fix_trainer_args():
    """修复trainer参数兼容性"""
    version_type = check_transformers_version()
    
    if version_type is None:
        return False
    
    trainer_file = "trainer.py"
    
    try:
        with open(trainer_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if version_type == "new":
            # 新版本使用 eval_strategy
            content = content.replace('evaluation_strategy=', 'eval_strategy=')
            print("✅ 已修复为新版本参数")
        else:
            # 旧版本使用 evaluation_strategy
            content = content.replace('eval_strategy=', 'evaluation_strategy=')
            print("✅ 已修复为旧版本参数")
        
        with open(trainer_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def main():
    print("🔧 检查并修复transformers兼容性...")
    
    if fix_trainer_args():
        print("✅ 兼容性修复完成，请重新运行训练")
    else:
        print("❌ 修复失败")

if __name__ == "__main__":
    main()
