"""
快速启动脚本 - 一键运行微调
"""

import os
import sys
import subprocess

def check_requirements():
    """检查环境要求"""
    print("检查环境要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    
    # 检查CUDA
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ CUDA可用")
        else:
            print("警告: 未检测到CUDA，将使用CPU训练（非常慢）")
    except FileNotFoundError:
        print("警告: 未找到nvidia-smi命令")
    
    return True

def install_dependencies():
    """安装依赖"""
    print("安装依赖包...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        print("✓ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("错误: 依赖安装失败")
        return False

def setup_data_path():
    """设置数据路径"""
    # 检查数据文件是否存在
    data_paths = [
        "tiaozhanbei-3/finetune_data/train_alpaca.jsonl",
        "../tiaozhanbei-3/finetune_data/train_alpaca.jsonl",
        "../../tiaozhanbei-3/finetune_data/train_alpaca.jsonl"
    ]
    
    for path in data_paths:
        if os.path.exists(path):
            print(f"✓ 找到数据文件: {path}")
            # 更新config.py中的路径
            update_config_path(path)
            return True
    
    print("错误: 未找到训练数据文件")
    print("请确保 train_alpaca.jsonl 文件在正确位置")
    return False

def update_config_path(data_path):
    """更新配置文件中的数据路径"""
    config_file = "config.py"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换数据路径
        content = content.replace(
            'DATA_PATH = "tiaozhanbei-3/finetune_data/train_alpaca.jsonl"',
            f'DATA_PATH = "{data_path}"'
        )
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ 已更新配置文件中的数据路径: {data_path}")

def main():
    """主函数"""
    print("=" * 50)
    print("DeepSeek 微调快速启动")
    print("=" * 50)
    
    # 1. 检查环境
    if not check_requirements():
        sys.exit(1)
    
    # 2. 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 3. 设置数据路径
    if not setup_data_path():
        sys.exit(1)
    
    # 4. 询问是否开始训练
    print("\n准备就绪！")
    response = input("是否开始训练？(y/n): ").strip().lower()
    
    if response == 'y' or response == 'yes':
        print("开始训练...")
        try:
            subprocess.run([sys.executable, 'main.py'], check=True)
        except subprocess.CalledProcessError:
            print("训练过程中出现错误")
            sys.exit(1)
    else:
        print("取消训练")
        print("您可以稍后运行: python main.py")

if __name__ == "__main__":
    main()
