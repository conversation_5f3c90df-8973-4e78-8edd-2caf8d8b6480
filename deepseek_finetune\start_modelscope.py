#!/usr/bin/env python3
"""
魔塔社区专用启动脚本
"""

import os
import sys
import subprocess

def install_modelscope():
    """安装ModelScope"""
    print("📦 安装ModelScope...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "modelscope"], check=True)
        print("✅ ModelScope安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ ModelScope安装失败")
        return False

def check_modelscope():
    """检查ModelScope是否可用"""
    try:
        import modelscope
        print(f"✅ ModelScope版本: {modelscope.__version__}")
        return True
    except ImportError:
        print("❌ ModelScope未安装")
        return False

def setup_environment():
    """设置环境"""
    print("🔧 设置环境变量...")
    
    # 设置ModelScope缓存目录
    os.environ["MODELSCOPE_CACHE"] = "./cache"
    
    # 禁用HuggingFace Hub
    os.environ["HF_HUB_OFFLINE"] = "1"
    
    # 设置CUDA
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    
    print("✅ 环境设置完成")

def test_model_access():
    """测试模型访问"""
    print("🔍 测试模型访问...")
    
    try:
        from modelscope import AutoTokenizer
        model_name = "deepseek-ai/deepseek-coder-7B-instruct-v1.5"
        
        print(f"尝试加载tokenizer: {model_name}")
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        print("✅ 模型访问正常")
        return True
    except Exception as e:
        print(f"❌ 模型访问失败: {e}")
        return False

def start_training():
    """开始训练"""
    print("🚀 开始训练...")
    
    try:
        from main import main as train_main
        train_main()
        return True
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 60)
    print("🎯 魔塔社区 DeepSeek微调启动器")
    print("使用ModelScope替代HuggingFace")
    print("=" * 60)
    
    # 1. 检查或安装ModelScope
    if not check_modelscope():
        if not install_modelscope():
            print("❌ 无法安装ModelScope")
            sys.exit(1)
    
    # 2. 设置环境
    setup_environment()
    
    # 3. 测试模型访问
    if not test_model_access():
        print("❌ 模型访问测试失败")
        print("请检查网络连接或模型名称")
        sys.exit(1)
    
    # 4. 开始训练
    print("\n🔥 准备开始训练...")
    print("使用魔塔社区模型，网络更稳定")
    print("-" * 40)
    
    if start_training():
        print("\n🎉 训练完成！")
        print("模型保存在: ./output/")
    else:
        print("\n❌ 训练失败")

if __name__ == "__main__":
    main()
