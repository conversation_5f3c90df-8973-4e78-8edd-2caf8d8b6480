from llama_index.core import SimpleDirectoryReader, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.llms.openai_like import OpenAILike
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# --- 配置 ---
DEEPSEEK_API_KEY = "***********************************"
DATA_DIR = "data"
OUTPUT_FILE = "high_quality_rag_dataset_500.jsonl"

print("🚀 --- 快速生成500条高质量数据集 ---")

# 1. 加载文档
print(f"步骤 1/3: 从 '{DATA_DIR}' 目录加载文档...")
documents = SimpleDirectoryReader(DATA_DIR, recursive=True).load_data()
print(f"文档加载完成，共 {len(documents)} 个文档。")

# 2. 分割文档
node_parser = SentenceSplitter(chunk_size=512)
nodes = node_parser.get_nodes_from_documents(documents)
print(f"步骤 2/3: 文档分割完成，共生成 {len(nodes)} 个节点。")

# 🔥 关键修复：只使用500个节点
target_samples = 500
nodes = nodes[:target_samples]
print(f"✅ 只使用前 {len(nodes)} 个节点，预计耗时约30-45分钟")

# 3. 初始化LLM
llm = OpenAILike(
    model="deepseek-chat",
    api_base="https://api.deepseek.com",
    api_key=DEEPSEEK_API_KEY,
    context_window=128000,
    is_chat_model=True,
    is_function_calling_model=False,
    max_tokens=2048,
    temperature=0.3,
    top_p=0.8,
    timeout=30,
    max_retries=2
)

def generate_qa_pair(node_data):
    """为单个节点生成问答对"""
    node, question_type = node_data
    try:
        context = node.text
        
        # 根据文本类型生成不同的问题
        if "时间:" in context and "闸前水位" in context:
            # 时序数据类型
            if question_type == "analysis":
                prompt = f"""你是水利工程专家。基于以下闸门运行数据，生成一个数据分析问题和答案。

数据：{context}

生成格式：
问题：[数据分析类问题，如最大值、趋势等]
答案：[基于数据的准确答案]"""
            elif question_type == "time":
                prompt = f"""你是水利工程专家。基于以下闸门运行数据，生成一个时间查询问题和答案。

数据：{context}

生成格式：
问题：[时间相关问题]
答案：[包含时间和数值的答案]"""
            elif question_type == "operation":
                prompt = f"""你是水利工程专家。基于以下闸门运行数据，生成一个运行状态问题和答案。

数据：{context}

生成格式：
问题：[运行状态分析问题]
答案：[专业的运行状态分析]"""
            else:  # comparison
                prompt = f"""你是水利工程专家。基于以下闸门运行数据，生成一个比较分析问题和答案。

数据：{context}

生成格式：
问题：[比较分析问题]
答案：[对比分析结果]"""
        else:
            # 法规文档类型
            prompt = f"""你是水利工程法规专家。基于以下文档，生成一个专业问题和答案。

文档：{context}

生成格式：
问题：[法规、管理或技术问题]
答案：[准确的专业解答]"""

        response = llm.complete(prompt)
        response_text = str(response).strip()
        
        # 解析问题和答案
        lines = response_text.split('\n')
        question = ""
        answer = ""
        
        for line in lines:
            line = line.strip()
            if line.startswith('问题：') or line.startswith('问题:'):
                question = line.replace('问题：', '').replace('问题:', '').strip()
            elif line.startswith('答案：') or line.startswith('答案:'):
                answer = line.replace('答案：', '').replace('答案:', '').strip()
        
        if question and answer:
            return {
                "query": question,
                "context": context,
                "response": answer
            }
        else:
            # 如果解析失败，使用原始响应
            return {
                "query": f"基于以下内容的专业问题：{context[:50]}...",
                "context": context,
                "response": response_text
            }
            
    except Exception as e:
        print(f"处理节点时出错: {e}")
        return None

# 4. 生成500条数据
print("步骤 3/3: 开始生成500条问答对...")
start_time = time.time()

# 定义问题类型
question_types = ["analysis", "time", "operation", "comparison"]

# 为每个节点分配问题类型
node_with_types = []
for i, node in enumerate(nodes):
    question_type = question_types[i % 4]
    node_with_types.append((node, question_type))

train_data = []
max_workers = 8

with ThreadPoolExecutor(max_workers=max_workers) as executor:
    future_to_data = {
        executor.submit(generate_qa_pair, node_data): i 
        for i, node_data in enumerate(node_with_types)
    }
    
    completed = 0
    failed = 0
    for future in as_completed(future_to_data):
        result = future.result()
        if result:
            train_data.append(result)
        else:
            failed += 1
        
        completed += 1
        elapsed = time.time() - start_time
        if completed % 50 == 0 or completed == len(node_with_types):
            success_rate = ((completed - failed) / completed) * 100
            avg_time = elapsed / completed
            eta = (len(node_with_types) - completed) * avg_time
            print(f"  ✅ {completed}/{len(node_with_types)} | 成功率: {success_rate:.1f}% | 剩余: {eta/60:.1f}分钟")

# 5. 保存结果
try:
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        for item in train_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    total_time = time.time() - start_time
    print(f"\n🎉 --- 生成完成！---")
    print(f"📁 文件: {OUTPUT_FILE}")
    print(f"✅ 成功: {len(train_data)}/500 条")
    print(f"⏱️ 耗时: {total_time/60:.1f} 分钟")
    print(f"💰 预计成本: ¥{len(train_data)*0.002:.2f}")
    
    if train_data:
        print(f"\n📝 示例:")
        example = train_data[0]
        print(f"问题: {example['query'][:100]}...")
        print(f"答案: {example['response'][:100]}...")
        
except Exception as e:
    print(f"保存文件时出错: {e}")
